{"name": "@docemenu/client", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "vite build", "build:client": "vite build", "dev": "vite", "preview": "vite preview", "check": "tsc --noEmit"}, "dependencies": {"@shared/schema": "file:../shared", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"vite": "^5.4.14", "@vitejs/plugin-react": "^4.3.2", "typescript": "5.6.3", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1"}}