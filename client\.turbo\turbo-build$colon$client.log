[?9001h[?1004h[?25l[2J[m[H]0;npm[?25h]0;npm run build:client[?25l
> @docemenu/client@1.0.0 build:client
> vite build[6;1H[?25h]0;C:\WINDOWS\system32\cmd.exe [36mvite v5.4.14 [32mbuilding for production...
[m[Ktransforming (1) [2mindex.html[22m
[K[27C[32m[2m
✓ [m[2m3 modules transformed.[22m[K
[31m[2mx [m[2mBuild failed in 861ms[22m[K
[31m[2merror during build:[m[K
[31m[2m[vite]: Rollup failed to resolve [m
[31m[2m[7;33H import "@/components/ui/theme-pro[m
[31m[2m[7;33Hovider" from "D:/Projetos/02 - Dev[m
[31m[2m[7;33Hv/replitDoceMenu/client/src/main.t[m
[31m[2m[7;33Htsx".[22m[K
[m
[31m[2mThis is most likely unintended be[m
[31m[2m[7;33Hecause it can break your applicati[m
[31m[2m[7;33Hion at runtime.[22m[K
[m
[31m[2mIf you do want to externalize thi[m
[31m[2m[7;33His module explicitly add it to[22m    [8;1H[m
[31m[2m`build.rollupOptions.external`[22m   [8;1H[m
[31m[2m    at viteWarn (file:///D:/Proje[m
[31m[2m[7;33Hetos/02%20-%20Dev/replitDoceMenu/n[m
[31m[2m[7;33Hnode_modules/vite/dist/node/chunks[m
[31m[2m[7;33Hs/dep-CHZK6zbr.js:65747:17)[22m       [8;1H[m
[31m[2m    at onRollupWarning (file:///D[m
[31m[2m[7;33HD:/Projetos/02%20-%20Dev/replitDoc[m
[31m[2m[7;33HceMenu/node_modules/vite/dist/node[m
[31m[2m[7;33He/chunks/dep-CHZK6zbr.js:65779:5)[22m [8;1H[m
[31m[2m    at onwarn (file:///D:/Projeto[m
[31m[2m[7;33Hos/02%20-%20Dev/replitDoceMenu/nod[m
[31m[2m[7;33Hde_modules/vite/dist/node/chunks/d[m
[31m[2m[7;33Hdep-CHZK6zbr.js:65442:7)[22m[K
[m
[31m[2m    at file:///D:/Projetos/02%20-[m
[31m[2m[7;33H-%20Dev/replitDoceMenu/node_module[m
[31m[2m[7;33Hes/rollup/dist/es/shared/node-entr[m
[31m[2m[7;33Hry.js:19452:13[22m[K
[m
[31m[2m    at Object.logger [as onLog] ([m
[31m[2m[7;33H(file:///D:/Projetos/02%20-%20Dev/[m
[31m[2m[7;33H/replitDoceMenu/node_modules/rollu[m
[31m[2m[7;33Hup/dist/es/shared/node-entry.js:21[m
[31m[2m[7;33H1178:9)[22m[K
[m
[31m[2m    at ModuleLoader.handleInvalid[m
[31m[2m[7;33HdResolvedId (file:///D:/Projetos/0[m
[31m[2m[7;33H02%20-%20Dev/replitDoceMenu/node_m[m
[31m[2m[7;33Hmodules/rollup/dist/es/shared/node[m
[31m[2m[7;33He-entry.js:20067:26)[22m[K
[m
[31m[2m    at file:///D:/Projetos/02%20-[m
[31m[2m[7;33H-%20Dev/replitDoceMenu/node_module[m
[31m[2m[7;33Hes/rollup/dist/es/shared/node-entr[m
[31m[2m[7;33Hry.js:20025:26[22m[K
[m
[37m[40m[2mnpm[m [31m[40mERR![m [35mLifecycle script `build:[m
[35m[7;33H:client` failed with error:       
[m
[37m[40mnpm[m [31m[40mERR![m [35mError: command failed   [8;1H[m
[37m[40mnpm[m [31m[40mERR![m   [35min workspace: @docemen[m
[35m[7;33Hnu/client@1.0.0
[m
[37m[40mnpm[m [31m[40mERR![m   [35mat location: D:\Projet[m
[35m[7;33Htos\02 - Dev\replitDoceMenu\client[m
[35m[7;33Ht[m

[37m[40mnpm[m [36m[40mnotice[m[K
[37m[40mnpm[m [36m[40mnotice[m New [31mmajor [mversion of n
[7;33Hnpm available! [31m10.4.0 [m-> [32m11.4.2   [8;1H[m
[37m[40mnpm[m [36m[40mnotice[m Changelog: [36mhttps://git[m
[36m[7;33Hthub.com/npm/cli/releases/tag/v11.[m
[36m[7;33H.4.2
[m
[37m[40mnpm[m [36m[40mnotice[m Run [32mnpm install -g npm[m
[32m[7;33Hm@11.4.2 [mto update![K
[37m[40mnpm[m [36m[40mnotice[m[K
[?9001l[?1004l
