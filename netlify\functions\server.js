const serverless = require('serverless-http');

// Importa o app Express do build
const app = require('../../dist/index.js');

// Exporta como função serverless do Netlify
const handler = serverless(app.default || app);

exports.handler = async (event, context) => {
  // Log para debug
  console.log('Netlify function called:', event.path);
  
  try {
    const result = await handler(event, context);
    return result;
  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal Server Error' })
    };
  }
};
