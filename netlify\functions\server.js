const serverless = require('serverless-http');
const path = require('path');

// Importa o app Express do build
let app;
try {
  app = require('../../dist/index.js');
  console.log('Express app loaded successfully');
} catch (error) {
  console.error('Failed to load Express app:', error);
  throw error;
}

// Exporta como função serverless do Netlify
const handler = serverless(app.default || app, {
  binary: ['image/*', 'application/pdf', 'application/octet-stream']
});

exports.handler = async (event, context) => {
  // Log para debug
  console.log('Netlify function called:', {
    path: event.path,
    method: event.httpMethod,
    headers: event.headers
  });

  try {
    // Configurar timeout
    context.callbackWaitsForEmptyEventLoop = false;

    const result = await handler(event, context);

    console.log('Function result:', {
      statusCode: result.statusCode,
      headers: result.headers
    });

    return result;
  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      },
      body: JSON.stringify({
        error: 'Internal Server Error',
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      })
    };
  }
};
