[?9001h[?1004h[?25l[2J[m[H]0;npm[?25h]0;npm run build[?25l
> @docemenu/client@1.0.0 build
> vite build[5;1H[?25h]0;C:\WINDOWS\system32\cmd.exe [36mvite v5.4.14 [32mbuilding for production...
[m[Ktransforming (1) [2mindex.html[22m
[K[27C
transforming (3) [2msrc\main.tsx[22m
[K[29C
transforming (9) [2m..\node_modules\@tansta[22m
[K[40C
[K[K[2mtransforming (30) ..\node_modules\react-[22m     [6;41H
[K[40C
[K[Ktransforming (74) [2msrc\context\Subscripti[22m
[K[40C[2m
transforming (102) ..\node_modules\@tans[22m
[K[40C
[K[2mtransforming (119) ..\node_modules\lucid[22m
[K[K[K[2mtransforming (122) src\lib\firebase.ts[22m
[K[38C
[K[2mtransforming (136) src\components\ui\ale[22m
[K[40C
[K[K[2mtransforming (152) src\components\ui\swi[22m
[K[K[2mtransforming (177) ..\node_modules\@radi[22m
[K[40C
[K[K[2mtransforming (209) ..\node_modules\lucid[22m
[K[40C[2m
transforming (309) ..\node_modules\lucid[22m
[K[40C[2m
transforming (422) ..\node_modules\lucid[22m
[K[40C[2m
transforming (506) ..\node_modules\lucid[22m
[K[40C[2m
transforming (629) ..\node_modules\lucid[22m
[K[40C[2m
transforming (749) ..\node_modules\lucid[22m
[K[40C[2m
transforming (849) ..\node_modules\lucid[22m
[K[40C[2m
transforming (969) ..\node_modules\lucid[22m
[K[40C[2m
transforming (1099) ..\node_modules\luci[22m
[K[40C[2m
transforming (1204) ..\node_modules\luci[22m
[K[40C[2m
transforming (1269) ..\node_modules\luci[22m
[K[40C[2m
transforming (1348) ..\node_modules\luci[22m
[K[40C[2m
transforming (1449) ..\node_modules\luci[22m
[K[40C[2m
transforming (1529) ..\node_modules\luci[22m
[K[40C[2m
transforming (1586) ..\node_modules\luci[22m
[K[40C[2m
transforming (1676) ..\node_modules\luci[22m
[K[40C
[2mtransforming (1749) ..\node_modules\fram[22m     [6;41H
[K[K[2mtransforming (1811) ..\node_modules\fram[22m     [6;41H
[K[40C
[K[K[K[K[K[K[2mtransforming (1886) ..\node_modules\date[22m     [6;41H
[K[40C[2m
transforming (1889) src\components\admin[22m
[K[40C[2m
transforming (1954) ..\node_modules\date[22m
[K[40C[2m
transforming (2031) ..\node_modules\date[22m
[K[40C[2m
transforming (2094) ..\node_modules\date[22m
[K[40C[2m
transforming (2157) ..\node_modules\reac[22m
[K[K[K[2mtransforming (2201) ..\node_modules\fram[22m
[K[40C
[K[K[2mtransforming (2280) ..\node_modules\date[22m
[K[40C[2m
transforming (2321) ..\node_modules\date[22m
[K[40C
[K[K[K[K[2mtransforming (2384) ..\node_modules\date[22m
[K[K[2mtransforming (2401) ..\node_modules\driz[22m
[K[40C[2m
transforming (2430) ..\node_modules\fram[22m
[K[40C
[K[2mtransforming (2447) ..\node_modules\fram[22m     [6;41H
[K[40C
[K[2mtransforming (2507) ..\node_modules\date[22m
[K[40C[2m
transforming (2607) ..\node_modules\date[22m
[K[40C[2m
transforming (2727) ..\node_modules\date[22m
[K[40C[2m
transforming (2827) ..\node_modules\date[22m
[K[40C
[2mtransforming (2928) ..\node_modules\rech[22m     [6;41H
[K[40C[2m
transforming (2948) ..\node_modules\rech[22m
[K[40C[2m
transforming (2966) ..\node_modules\rech[22m
[K[40C
[K[2mtransforming (2984) ..\node_modules\@bab[22m
[K[40C[2m
transforming (3012) ..\node_modules\date[22m
[K[40C[2m
transforming (3070) ..\node_modules\driz[22m
[K[40C[2m
transforming (3115) ..\node_modules\fram[22m
[K[40C[2m
transforming (3169) ..\node_modules\rech[22m
[K[40C[2m
transforming (3183) ..\node_modules\rech[22m
[K[40C[2m
transforming (3199) ..\node_modules\raf\[22m
[K[40C[2m
transforming (3228) ..\node_modules\loda[22m
[K[40C[2m
transforming (3247) ..\node_modules\core[22m
[K[40C[2m
transforming (3269) ..\node_modules\core[22m
[K[40C[2m
transforming (3322) ..\node_modules\core[22m
[K[40C[2m
transforming (3382) ..\node_modules\d3-s[22m
[K[40C[2m
transforming (3437) ..\node_modules\d3-s[22m
[K[40C[2m
transforming (3460) ..\node_modules\core[22m
[K[40C[2m
transforming (3519) ..\node_modules\d3-f[22m
[K[40C[2m
transforming (3553) ..\node_modules\loda[22m
[K[40C[2m
transforming (3695) ..\node_modules\d3-t[22m
[K[40C[2m
transforming (3882) ..\node_modules\core[22m
[K[40C[2m
transforming (4125) ..\node_modules\loda[22m
[K[40C
[K[K[32m[2m✓ [m[2m4299 modules transformed.
[22m[K[K[K[K[2mtransforming (4308) ..\node_modules\@fir[22m
[K[40C[2m
rendering chunks (1)...[22m
[K[23C[2m
rendering chunks (2)...[22m
[K[23C
[2mrendering chunks (3)...[22m
[K[23C[2m
rendering chunks (4)...[22m
[K[23C[2m
rendering chunks (6)...[22m[K
[K[23C[2m
rendering chunks (7)...[22m[2m
rendering chunks (9)...[22m[K
[K[23C[2m
rendering chunks (10)...[22m
[K[24C[2m
rendering chunks (11)...[22m
[K[24C[2m
rendering chunks (12)...[22m[2m
rendering chunks (13)...[22m[K
[K[24C[33m[1m[2m
[plugin:vite:reporter][m [33m[plugin vite:reporter] 
(!) D:/Projetos/02 - Dev/replitDoceMenu/client/src/lib/firebase.ts is dynamically imported by D:/Projetos/02 - Dev/replitDoceMenu/client/src/lib/queryClient.ts, D:/Projetos/02 - Dev/replitDoceMenu/client/src/lib/queryClient.ts, D:/Projetos/02 - Dev/replitDoceMenu/client/src/lib/queryClient.ts, D:/Projetos/02 - Dev/replitDoceMenu/client/src/lib/uploadUtils.ts, D:/Projetos/02 - Dev/replitDoceMenu/client/src/lib/uploadUtils.ts, D:/Projetos/02 - Dev/replitDoceMenu/client/src/lib/uploadUtils.ts, D:/Projetos/02 - Dev/replitDoceMenu/client/src/pages/admin/orders/select-customer.tsx but also statically imported by D:/Projetos/02 - Dev/replitDoceMenu/client/src/hooks/useFirebaseAuth.ts, D:/Projetos/02 - Dev/replitDoceMenu/client/src/pages/admin/orders/custom-produ[m
[33m[24;45Huct.tsx, D:/Projetos/02 - Dev/replitDoceMenu/c[m
[33m[24;45Hclient/src/pages/admin/orders/product-details/[m
[33m[24;45H/[id].tsx, dynamic import will not move module[m
[33m[24;45He into another chunk.
[m
[33m[m
[33m[1m[plugin:vite:reporter][m [33m[plugin vite:reporter][m
[33m[24;45H]
[m
[33m(!) D:/Projetos/02 - Dev/replitDoceMenu/clien[m
[33m[24;45Hnt/src/lib/queryClient.ts is dynamically impor[m
[33m[24;45Hrted by D:/Projetos/02 - Dev/replitDoceMenu/cl[m
[33m[24;45Hlient/src/components/admin/dashboard/Dashboard[m
[33m[24;45HdMVP.tsx, D:/Projetos/02 - Dev/replitDoceMenu/[m
[33m[24;45H/client/src/components/admin/dashboard/Dashboa[m
[33m[24;45HardMVP.tsx, D:/Projetos/02 - Dev/replitDoceMen[m
[33m[24;45Hnu/client/src/components/admin/dashboard/Dashb[m
[33m[24;45HboardMVP.tsx but also statically imported by D[m
[33m[24;45HD:/Projetos/02 - Dev/replitDoceMenu/client/src[m
[33m[24;45Hc/components/admin/CouponManagement.tsx, D:/Pr[m
[33m[24;45Hrojetos/02 - Dev/replitDoceMenu/client/src/com[m
[33m[24;45Hmponents/admin/OrderManagement.tsx, D:/Projeto[m
[33m[24;45Hos/02 - Dev/replitDoceMenu/client/src/componen[m
[33m[24;45Hnts/admin/ProductManagement.tsx, D:/Projetos/0[m
[33m[24;45H02 - Dev/replitDoceMenu/client/src/context/Sto[m
[33m[24;45HoreContext.tsx, D:/Projetos/02 - Dev/replitDoc[m
[33m[24;45HceMenu/client/src/context/SubscriptionContext.[m
[33m[24;45H.tsx, D:/Projetos/02 - Dev/replitDoceMenu/clie[m
[33m[24;45Hent/src/hooks/useFinancialData.ts, D:/Projetos[m
[33m[24;45Hs/02 - Dev/replitDoceMenu/client/src/hooks/use[m
[33m[24;45HeFirebaseAuth.ts, D:/Projetos/02 - Dev/replitD[m
[33m[24;45HDoceMenu/client/src/hooks/useGlobalAdmin.ts, D[m
[33m[24;45HD:/Projetos/02 - Dev/replitDoceMenu/client/src[m
[33m[24;45Hc/hooks/useOrderPayments.ts, D:/Projetos/02 - [m
[33m[24;45H Dev/replitDoceMenu/client/src/hooks/useStoreT[m
[33m[24;45HTrial.ts, D:/Projetos/02 - Dev/replitDoceMenu/[m
[33m[24;45H/client/src/main.tsx, D:/Projetos/02 - Dev/rep[m
[33m[24;45HplitDoceMenu/client/src/pages/admin/categories[m
[33m[24;45Hs/[id]/edit.tsx, D:/Projetos/02 - Dev/replitDo[m
[33m[24;45HoceMenu/client/src/pages/admin/categories/inde[m
[33m[24;45Hex.tsx, D:/Projetos/02 - Dev/replitDoceMenu/cl[m
[33m[24;45Hlient/src/pages/admin/categories/new.tsx, D:/P[m
[33m[24;45HProjetos/02 - Dev/replitDoceMenu/client/src/pa[m
[33m[24;45Hages/admin/coupons/[id]/edit.tsx, D:/Projetos/[m
[33m[24;45H/02 - Dev/replitDoceMenu/client/src/pages/admi[m
[33m[24;45Hin/coupons/new.tsx, D:/Projetos/02 - Dev/repli[m
[33m[24;45HitDoceMenu/client/src/pages/admin/customers/[i[m
[33m[24;45Hid].tsx, D:/Projetos/02 - Dev/replitDoceMenu/c[m
[33m[24;45Hclient/src/pages/admin/customers/new.tsx, D:/P[m
[33m[24;45HProjetos/02 - Dev/replitDoceMenu/client/src/pa[m
[33m[24;45Hages/admin/global/settings.tsx, D:/Projetos/02[m
[33m[24;45H2 - Dev/replitDoceMenu/client/src/pages/admin/[m
[33m[24;45H/orders/[id].tsx, D:/Projetos/02 - Dev/replitD[m
[33m[24;45HDoceMenu/client/src/pages/admin/orders/edit-it[m
[33m[24;45Htems/[id].tsx, D:/Projetos/02 - Dev/replitDoce[m
[33m[24;45HeMenu/client/src/pages/admin/orders/new.tsx, D[m
[33m[24;45HD:/Projetos/02 - Dev/replitDoceMenu/client/src[m
[33m[24;45Hc/pages/admin/orders/select-customer.tsx, D:/P[m
[33m[24;45HProjetos/02 - Dev/replitDoceMenu/client/src/pa[m
[33m[24;45Hages/admin/orders/select-products.tsx, D:/Proj[m
[33m[24;45Hjetos/02 - Dev/replitDoceMenu/client/src/pages[m
[33m[24;45Hs/admin/products/edit.tsx, D:/Projetos/02 - De[m
[33m[24;45Hev/replitDoceMenu/client/src/pages/admin/produ[m
[33m[24;45Hucts/new.tsx, D:/Projetos/02 - Dev/replitDoceM[m
[33m[24;45HMenu/client/src/pages/store/CartPage.tsx, D:/P[m
[33m[24;45HProjetos/02 - Dev/replitDoceMenu/client/src/pa[m
[33m[24;45Hages/test-ios-layout.tsx, dynamic import will [m
[33m[24;45H not move module into another chunk.
[m
[33m[m
rendering chunks (14)...
[K[24C
[Kcomputing gzip size (14)...[K
computing gzip size (15)...[K[2m
../dist/public/[32m[22mindex.html                    [25;66H[m
[32m      [m[1m[2m  1.55 kB[22m[2m │ gzip:   0.57 kB[32m[22m[K
[m
[2m../dist/public/[35m[22massets/index-MLNUfnCP.css     [m
[35m[24;45H 
      [m[1m[2m117.33 kB[22m[2m │ gzip:  18.90 kB[35m[22m[K
[m
[2m../dist/public/[36m[22massets/purify.es-CF4_YkFU.js  [m
[36m[24;45H 
      [m[1m[2m 21.86 kB[22m[2m │ gzip:   8.62 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/icons-vendor-DYiem_zI.j[m
[36m[24;45Hjs     [m[1m[2m 37.47 kB[22m[2m │ gzip:   7.76 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/query-vendor-C060jWkV.j[m
[36m[24;45Hjs     [m[1m[2m 38.66 kB[22m[2m │ gzip:  11.55 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/utils-vendor-DvKWi2K0.j[m
[36m[24;45Hjs     [m[1m[2m 46.42 kB[22m[2m │ gzip:  14.36 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/i18n-vendor-ChWGh-ir.js[m
[36m[24;45Hs
      [m[1m[2m 49.91 kB[22m[2m │ gzip:  15.55 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/form-vendor-CcLQAj0k.js[m
[36m[24;45Hs
      [m[1m[2m 90.46 kB[22m[2m │ gzip:  25.24 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/motion-vendor-HvLkLPKO.[m
[36m[24;45H.js    [m[1m[2m111.24 kB[22m[2m │ gzip:  36.45 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/ui-vendor-qEKiwxyg.js  [m
[36m[24;45H 
      [m[1m[2m132.06 kB[22m[2m │ gzip:  41.70 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/react-vendor--0RGCJ5T.j[m
[36m[24;45Hjs     [m[1m[2m141.41 kB[22m[2m │ gzip:  45.48 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/index.es-DJdO8dvP.js   [m
[36m[24;45H 
      [m[1m[2m150.03 kB[22m[2m │ gzip:  51.26 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/firebase-vendor-BcWLCle[m
[36m[24;45Hev.js  [m[1m[2m165.65 kB[22m[2m │ gzip:  33.98 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/charts-vendor-CER55TbO.[m
[36m[24;45H.js    [m[1m[2m382.91 kB[22m[2m │ gzip: 105.07 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/pdf-vendor-Dm0WbtDp.js [m
[36m[24;45H 
      [m[1m[2m560.41 kB[22m[2m │ gzip: 166.44 kB[36m[22m[K
[m
[2m../dist/public/[36m[22massets/index-Bj0FclmB.js      [m
[36m[24;45H 
      [m[1m[2m891.97 kB[22m[2m │ gzip: 206.07 kB[36m[22m[K
[m
[32m✓ built in 18.60s
[m
[?9001l[?1004l
