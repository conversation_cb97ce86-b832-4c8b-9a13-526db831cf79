# Variáveis de Ambiente para Netlify

Configure estas variáveis no painel do Netlify (Site settings > Environment variables):

## Firebase (Método Recomendado - Variáveis Separadas)
```
FIREBASE_PROJECT_ID=replitdocemenu
FIREBASE_PRIVATE_KEY_ID=88aff8c3051991cae227d59cfbdf3a436ec90875
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=113390595850314690011
```

**FIREBASE_PRIVATE_KEY** (copie exatamente como está, incluindo as quebras de linha):
```
-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCiMHZcgfvxCBY6
...
-----END PRIVATE KEY-----
```

## Outras Variáveis
```
VITE_APP_ENVIRONMENT=production
SESSION_SECRET=N92K47NDHeGn/Rjzj8pagZFnlTpxFj31/Ap31tKvRa7HqHlHh4ffAmXwAnQCy0Hnpum9BNSdVGL71oBzAgCKfw==

VITE_SUPABASE_URL=https://udkkdwvgobazxnowbtjk.supabase.co
SUPABASE_URL=https://udkkdwvgobazxnowbtjk.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVka2tkd3Znb2Jhenhub3didGprIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjU0MDc0MiwiZXhwIjoyMDYyMTE2NzQyfQ.W5v47sYl5xWQaJe6tAzB-qICQd4HJaJbE06SBTc4F-k

DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
USE_SUPABASE_POOL=true

STRIPE_SECRET_KEY=sk_test_51NOPDYEydRrfydtK5SIDmDrxeXuWHSfDJOttgWaeSfJamRoRuEWaViLK5SC15y817OsFgbK3bNeUncU8U6yrc29w004aXTPq4N
STRIPE_PUBLISHABLE_KEY=pk_test_51NOPDYEydRrfydtKlGXD9UIoTo53ZgPfDPxPNAIWoLCH1F7JH9FnI76YPYjAqorPW5d9rnGL9gyk6txhmtnv4eP400V3gQJsWR
STRIPE_WEBHOOK_SECRET=whsec_gxIEfmE4mAQMRB1HRImMWONdtYtUQuO8
STRIPE_PREMIUM_MONTHLY_PRICE_ID=price_1R8nbCEydRrfydtKJoqmVQYP
STRIPE_PREMIUM_YEARLY_PRICE_ID=price_1R8nbCEydRrfydtK5NTPTP1r

STRIPE_SUCCESS_URL=https://replitdocemenu.netlify.app/admin/settings?subscription=success
STRIPE_CANCEL_URL=https://replitdocemenu.netlify.app/admin/settings?subscription=canceled
BASE_URL=https://replitdocemenu.netlify.app
NODE_ENV=production
PORT=8888
```

## Instruções:
1. Vá para o painel do Netlify
2. Site settings > Environment variables
3. Adicione cada variável individualmente
4. Para FIREBASE_PRIVATE_KEY, cole a chave completa incluindo as linhas BEGIN/END
5. Redeploy o site após adicionar todas as variáveis
