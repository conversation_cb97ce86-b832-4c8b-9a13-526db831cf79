#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Netlify build...');

try {
  // 1. Install root dependencies first
  console.log('⚙️ Installing root dependencies...');
  execSync('npm install --legacy-peer-deps', {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // 2. Create dist directory
  console.log('📁 Creating dist directory...');
  if (!fs.existsSync('dist')) {
    fs.mkdirSync('dist', { recursive: true });
  }

  // 3. Install client dependencies
  console.log('📦 Installing client dependencies...');
  execSync('cd client && npm install --legacy-peer-deps', {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // 3.1. Verify Tailwind installation
  console.log('🔍 Verifying Tailwind installation...');
  try {
    execSync('cd client && npx tailwindcss --version', {
      stdio: 'inherit',
      cwd: process.cwd()
    });
  } catch (error) {
    console.log('⚠️ Tailwind not found, installing manually...');
    execSync('cd client && npm install tailwindcss autoprefixer postcss --save-dev', {
      stdio: 'inherit',
      cwd: process.cwd()
    });
  }

  // 4. Build client (frontend) - try multiple approaches
  console.log('🔨 Building client...');

  try {
    // Try using npx vite directly (should work now that server doesn't import vite)
    execSync('cd client && npx vite build --config vite.config.mjs', {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    console.log('✅ Vite build successful with .mjs config');
  } catch (error) {
    console.log('Vite with .mjs config failed, trying without config...');
    try {
      execSync('cd client && npx vite build --outDir ../dist/public --emptyOutDir', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log('✅ Vite build successful without config');
    } catch (error2) {
      console.log('Vite build failed completely, this should not happen now...');
      console.error('Vite error:', error2.message);
      process.exit(1);
    }
  }

  // 5. Build server (backend) using npx
  console.log('⚙️ Building server...');
  execSync('npx esbuild server/index.ts --platform=node --packages=external --bundle --format=cjs --outfile=dist/index.js --external:vite --external:@vitejs/plugin-react --external:./vite', {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // 6. Verify build outputs
  console.log('🔍 Verifying build outputs...');

  if (!fs.existsSync('dist/public')) {
    console.error('❌ Frontend build not found at dist/public');
    process.exit(1);
  }

  if (!fs.existsSync('dist/index.js')) {
    console.error('❌ Backend build not found at dist/index.js');
    process.exit(1);
  }

  console.log('✅ Build completed successfully!');
  console.log('📁 Frontend built to: dist/public');
  console.log('⚙️ Backend built to: dist/index.js');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  console.error('Error details:', error);
  process.exit(1);
}
