#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Netlify build...');

try {
  // 1. Install root dependencies first
  console.log('⚙️ Installing root dependencies...');
  execSync('npm install --legacy-peer-deps', {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // 2. Create dist directory
  console.log('📁 Creating dist directory...');
  if (!fs.existsSync('dist')) {
    fs.mkdirSync('dist', { recursive: true });
  }

  // 3. Build client (frontend) using npx
  console.log('🔨 Building client...');
  execSync('cd client && npx vite build --outDir ../dist/public', {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // 4. Build server (backend) using npx
  console.log('⚙️ Building server...');
  execSync('npx esbuild server/index.ts --platform=node --packages=external --bundle --format=cjs --outfile=dist/index.js', {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // 5. Verify build outputs
  console.log('🔍 Verifying build outputs...');

  if (!fs.existsSync('dist/public')) {
    console.error('❌ Frontend build not found at dist/public');
    process.exit(1);
  }

  if (!fs.existsSync('dist/index.js')) {
    console.error('❌ Backend build not found at dist/index.js');
    process.exit(1);
  }

  console.log('✅ Build completed successfully!');
  console.log('📁 Frontend built to: dist/public');
  console.log('⚙️ Backend built to: dist/index.js');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  console.error('Error details:', error);
  process.exit(1);
}
