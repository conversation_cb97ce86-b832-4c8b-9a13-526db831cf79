#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Netlify build...');

try {
  // 1. Build client (frontend)
  console.log('📦 Building client...');
  execSync('cd client && npm install --legacy-peer-deps && npm run build', { 
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // 2. Copy client build to dist/public
  console.log('📁 Copying client build...');
  if (!fs.existsSync('dist')) {
    fs.mkdirSync('dist', { recursive: true });
  }
  
  if (fs.existsSync('client/dist')) {
    execSync('cp -r client/dist dist/public', { stdio: 'inherit' });
  }

  // 3. Build server
  console.log('⚙️ Building server...');
  execSync('cd server && npm install --legacy-peer-deps', { 
    stdio: 'inherit',
    cwd: process.cwd()
  });
  
  // Build server with esbuild
  execSync('npx esbuild server/index.ts --platform=node --packages=external --bundle --format=cjs --outfile=dist/index.js', { 
    stdio: 'inherit',
    cwd: process.cwd()
  });

  console.log('✅ Build completed successfully!');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
