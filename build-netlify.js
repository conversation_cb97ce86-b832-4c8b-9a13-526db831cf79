#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Netlify build...');

try {
  // 1. Install root dependencies first
  console.log('⚙️ Installing root dependencies...');
  execSync('npm install --legacy-peer-deps', {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // 2. Build using the existing build script (which handles both client and server)
  console.log('🔨 Building application...');
  execSync('npm run build', {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // 3. Verify build outputs
  console.log('🔍 Verifying build outputs...');

  if (!fs.existsSync('dist/public')) {
    console.error('❌ Frontend build not found at dist/public');
    process.exit(1);
  }

  if (!fs.existsSync('dist/index.js')) {
    console.error('❌ Backend build not found at dist/index.js');
    process.exit(1);
  }

  console.log('✅ Build completed successfully!');
  console.log('📁 Frontend built to: dist/public');
  console.log('⚙️ Backend built to: dist/index.js');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  console.error('Error details:', error);
  process.exit(1);
}
