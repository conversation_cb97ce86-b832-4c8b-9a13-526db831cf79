{"name": "@docemenu/server", "version": "1.0.0", "private": true, "type": "commonjs", "scripts": {"build": "esbuild index.ts --platform=node --packages=external --bundle --format=cjs --outfile=../dist/index.js", "build:server": "esbuild index.ts --platform=node --packages=external --bundle --format=cjs --outfile=../dist/index.js", "dev": "tsx index.ts", "check": "tsc --noEmit"}, "dependencies": {"@shared/schema": "file:../shared"}}