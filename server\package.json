{"name": "@docemenu/server", "version": "1.0.0", "private": true, "type": "commonjs", "scripts": {"build": "esbuild index.ts --platform=node --packages=external --bundle --format=cjs --outfile=../dist/index.js", "build:server": "esbuild index.ts --platform=node --packages=external --bundle --format=cjs --outfile=../dist/index.js", "dev": "tsx index.ts", "check": "tsc --noEmit"}, "dependencies": {"@shared/schema": "file:../shared"}, "devDependencies": {"esbuild": "^0.25.0", "tsx": "^4.19.1", "typescript": "5.6.3"}}