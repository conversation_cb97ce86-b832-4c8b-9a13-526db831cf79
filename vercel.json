{"version": 2, "builds": [{"src": "api/server.js", "use": "@vercel/node"}, {"src": "dist/public/**/*", "use": "@vercel/static"}], "buildCommand": "npm run build", "routes": [{"src": "/api/(.*)", "dest": "/api/server"}, {"handle": "filesystem"}, {"src": "/(.*)", "dest": "/dist/public/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, PATCH, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}