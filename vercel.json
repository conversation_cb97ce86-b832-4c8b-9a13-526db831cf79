{"version": 2, "builds": [{"src": "dist/index.js", "use": "@vercel/node"}, {"src": "dist/public/**/*", "use": "@vercel/static"}], "routes": [{"src": "/api/(.*)", "dest": "/dist/index.js", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"], "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, POST, PUT, PATCH, DELETE, OPTIONS", "Access-Control-Allow-Headers": "Content-Type, Authorization"}}, {"src": "/assets/(.*)", "dest": "/dist/public/assets/$1", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"handle": "filesystem"}, {"src": "/(.*)", "dest": "/dist/public/index.html", "headers": {"Cache-Control": "public, max-age=0, must-revalidate"}}], "buildCommand": "npm run build:turbo", "outputDirectory": "dist", "env": {"NODE_ENV": "production"}}