{"buildCommand": "npm run build", "outputDirectory": "dist/public", "installCommand": "npm install", "functions": {"api/server.js": {"runtime": "@vercel/node@3"}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/server"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, PATCH, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}